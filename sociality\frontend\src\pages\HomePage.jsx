import React from 'react';
import {
    <PERSON>,
    Flex,
    Spinner,
    useColorModeValue,
    Text,
    Tabs,
    TabList,
    Tab,
} from "@chakra-ui/react";
import { useEffect, useState } from "react";
import useShowToast from "../hooks/useShowToast";
import Post from "../components/post/Post";
import { useRecoilState, useRecoilValue } from "recoil";
import { postsAtom, userAtom } from "../atoms";
import SuggestedUsers from "../components/SuggestedUsers";
import CreatePost from "../components/CreatePost";
import { fetchWithSession } from "../utils/api";
import PokemonEmptyState from "../components/PokemonEmptyState";
import "../components/NoBorderTab.css";
import "../styles/ResponsiveHomepage.css";

const HomePage = () => {
    const [posts, setPosts] = useRecoilState(postsAtom);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState(0); // 0 = For You, 1 = Following
    const showToast = useShowToast();
    const user = useRecoilValue(userAtom);

    // Theme-aware colors
    const textColor = useColorModeValue("gray.800", "white");
    const spinnerColor = useColorModeValue("gray.600", "whiteAlpha.700");

    useEffect(() => {
        const getFeedPosts = async () => {
            setLoading(true);
            setPosts([]);
            try {
                let res;
                if (activeTab === 0) {
                    // For You feed
                    res = await fetchWithSession('/api/posts/for-you');
                } else {
                    // Following feed
                    res = await fetchWithSession('/api/posts/following');
                }

                if (res.ok) {
                    const data = await res.json();
                    if (Array.isArray(data)) {
                        setPosts(data);
                    } else {
                        showToast("Error", "Invalid data format", "error");
                    }
                } else {
                    const errorData = await res.json().catch(() => ({ error: 'Failed to fetch posts' }));
                    showToast("Error", errorData.error || 'Failed to fetch posts', "error");
                }
            } catch (error) {
                console.error('Homepage posts fetch error:', error);

                // Handle different error types
                if (error.message.includes('401')) {
                    console.log('Authentication error on homepage, user might need to re-login');
                    showToast("Info", "Please refresh the page or log in again to see posts", "info");
                } else {
                    showToast("Error", error.message || 'Failed to fetch posts', "error");
                }
            } finally {
                setLoading(false);
            }
        };

        // Small delay for new users who just completed profile setup
        const delay = user && user.isProfileComplete ? 500 : 0;
        setTimeout(getFeedPosts, delay);
    }, [showToast, setPosts, user, activeTab]); // Added activeTab to dependencies

    // Handle tab change
    const handleTabChange = (index) => {
        setActiveTab(index);
    };

    const handlePostCreated = (newPost) => {
        setPosts((prevPosts) => [newPost, ...prevPosts]); // Add the new post to the top of the feed
    };

    return (
        <Box w="full" maxW="100vw" overflowX="hidden" className="homepage-container">
            {/* Responsive Container */}
            <Flex
                direction="column"
                align="center"
                w="full"
                px={{ base: 2, xs: 3, sm: 4, md: 6 }}
                pt={{ base: 4, sm: 5, md: 6, lg: "80px", xl: "90px" }} // Add top padding to avoid logo overlap
                gap={{ base: 4, sm: 5, md: 6 }}
            >
                {/* Main Content */}
                <Box
                    w="full"
                    maxW={{
                        base: "100%",
                        xs: "100%",
                        sm: "100%",
                        md: "100%",
                        lg: "600px",
                        xl: "650px",
                        "2xl": "700px"
                    }}
                    position="relative"
                    zIndex={1}
                >
                    {/* Create Post Component */}
                    <Box mb={{ base: 4, sm: 5, md: 6 }}>
                        <CreatePost onPostCreated={handlePostCreated} />
                    </Box>

                    {/* Feed Tabs - Responsive */}
                    <Box mb={{ base: 4, sm: 5, md: 6 }}>
                        <Tabs
                            index={activeTab}
                            onChange={handleTabChange}
                            variant='unstyled'
                            w="full"
                            border="none"
                            style={{ border: "none" }}
                            className="no-border-tabs"
                        >
                            <TabList
                                justifyContent="center"
                                border="none"
                                style={{ border: "none" }}
                                className="no-border-tablist"
                                gap={{ base: 2, sm: 3, md: 4 }}
                                flexWrap="nowrap"
                                overflowX="auto"
                                sx={{
                                    '&::-webkit-scrollbar': {
                                        display: 'none'
                                    },
                                    scrollbarWidth: 'none'
                                }}
                            >
                                <Tab
                                    as="div"
                                    borderRadius={{ base: "lg", md: "md" }}
                                    bg="rgba(0, 0, 0, 0.2)"
                                    backdropFilter="blur(8px)"
                                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                                    px={{ base: 4, xs: 5, sm: 6, md: 8 }}
                                    py={{ base: 2.5, sm: 3 }}
                                    transition="all 0.3s ease"
                                    border="none"
                                    minW={{ base: "100px", sm: "120px" }}
                                    flexShrink={0}
                                    style={{
                                        border: "none",
                                        outline: "none",
                                        borderWidth: 0,
                                        borderStyle: "none"
                                    }}
                                    _selected={{
                                        bg: "rgba(0, 204, 133, 0.2)",
                                        boxShadow: "0 4px 18px rgba(0, 204, 133, 0.25)",
                                        border: "none",
                                        borderWidth: 0
                                    }}
                                    _hover={{
                                        bg: "rgba(0, 204, 133, 0.1)",
                                        boxShadow: "0 4px 15px rgba(0, 204, 133, 0.15)",
                                        border: "none",
                                        borderWidth: 0
                                    }}
                                    _focus={{
                                        border: "none",
                                        outline: "none",
                                        borderWidth: 0
                                    }}
                                    className="glass-tab no-border-tab"
                                    color={textColor}
                                    fontWeight="medium"
                                    fontSize={{ base: "sm", sm: "md" }}
                                >
                                    For you
                                </Tab>
                                <Tab
                                    as="div"
                                    borderRadius={{ base: "lg", md: "md" }}
                                    bg="rgba(0, 0, 0, 0.2)"
                                    backdropFilter="blur(8px)"
                                    boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                                    px={{ base: 4, xs: 5, sm: 6, md: 8 }}
                                    py={{ base: 2.5, sm: 3 }}
                                    transition="all 0.3s ease"
                                    border="none"
                                    minW={{ base: "100px", sm: "120px" }}
                                    flexShrink={0}
                                    style={{
                                        border: "none",
                                        outline: "none",
                                        borderWidth: 0,
                                        borderStyle: "none"
                                    }}
                                    _selected={{
                                        bg: "rgba(0, 204, 133, 0.2)",
                                        boxShadow: "0 4px 18px rgba(0, 204, 133, 0.25)",
                                        border: "none",
                                        borderWidth: 0
                                    }}
                                    _hover={{
                                        bg: "rgba(0, 204, 133, 0.1)",
                                        boxShadow: "0 4px 15px rgba(0, 204, 133, 0.15)",
                                        border: "none",
                                        borderWidth: 0
                                    }}
                                    _focus={{
                                        border: "none",
                                        outline: "none",
                                        borderWidth: 0
                                    }}
                                    className="glass-tab no-border-tab"
                                    color={textColor}
                                    fontWeight="medium"
                                    fontSize={{ base: "sm", sm: "md" }}
                                >
                                    Following
                                </Tab>
                            </TabList>
                        </Tabs>
                    </Box>

                    {/* Posts Section - Responsive */}
                    {loading && (
                        <Flex justify="center" my={{ base: 4, sm: 5, md: 6 }}>
                            <Spinner size="xl" color={spinnerColor} />
                        </Flex>
                    )}

                    {!loading && posts.length === 0 && (
                        <Box px={{ base: 2, sm: 4 }}>
                            <PokemonEmptyState
                                message={
                                    activeTab === 0
                                        ? "No posts to display. Try following some users or check back later!"
                                        : "No posts from people you follow. Try following some users to see their posts here!"
                                }
                            />
                        </Box>
                    )}

                    {/* Posts Feed - Responsive */}
                    <Box w="full">
                        {Array.isArray(posts) &&
                            posts.map((post, index) => (
                                <React.Fragment key={post._id}>
                                    <Box mb={{ base: 3, sm: 4, md: 5 }}>
                                        <Post post={post} isPostPage={false} />
                                    </Box>
                                    {index === 1 && ( // Show SuggestedUsers only after the second post (index 1)
                                        <Box my={{ base: 4, sm: 5, md: 6 }}>
                                            <SuggestedUsers />
                                        </Box>
                                    )}
                                </React.Fragment>
                            ))}
                    </Box>

                    {!loading && !Array.isArray(posts) && (
                        <Box textAlign="center" my={{ base: 4, sm: 5, md: 6 }} px={{ base: 2, sm: 4 }}>
                            <Text fontSize={{ base: "md", sm: "lg" }} color={textColor}>
                                Error loading posts. Please try again later.
                            </Text>
                        </Box>
                    )}
                </Box>
            </Flex>
        </Box>
    );
};

export default HomePage;
